# 🚨 WEBHOOK EMERGENCY GUIDE

## IMMEDIATE ACTION REQUIRED

### 1. **Fix the Current Issue (2 minutes)**

**Problem**: "Verify JWT with legacy secret" is breaking your Stripe webhooks

**Solution**:
1. Go to [Supabase Dashboard](https://supabase.com/dashboard/project/peuelelfodoiyvmjscfg)
2. Navigate to **Settings → API**
3. Find **"Verify JWT with legacy secret"**
4. **DISABLE IT** (turn OFF/uncheck)
5. ✅ **Webhooks will work immediately**

### 2. **Deploy Monitoring System (5 minutes)**

```bash
# Run the automated deployment
./scripts/deploy-webhook-monitoring.sh
```

**If deployment script fails, run manually**:
```bash
# Deploy database changes
supabase db push

# Deploy monitoring functions
supabase functions deploy webhook-monitor --no-verify-jwt
supabase functions deploy scheduled-webhook-monitor --no-verify-jwt

# Set required environment variables
supabase secrets set RESEND_API_KEY=your_resend_key
supabase secrets set ADMIN_EMAIL=<EMAIL>
supabase secrets set FROM_EMAIL=<EMAIL>
```

### 3. **Verify Fix (1 minute)**

Test webhook accessibility:
```bash
# Should return 400 (bad signature), NOT 401 (unauthorized)
curl -X POST https://peuelelfodoiyvmjscfg.supabase.co/functions/v1/stripe-webhook \
  -H "Content-Type: application/json" \
  -H "Stripe-Signature: test" \
  -d '{"test": true}'
```

**Expected result**: HTTP 400 (not 401)

## MONITORING DASHBOARD

After deployment, check your admin dashboard at `/admin` to see:
- 🟢 **Webhook Health Status**
- 📊 **Real-time Monitoring**
- 🚨 **Critical Alerts**
- 📈 **Historical Data**

## CRITICAL SETTINGS CHECKLIST

### ✅ Correct Configuration
- [ ] "Verify JWT with legacy secret" = **DISABLED**
- [ ] `stripe-webhook` function has `verify_jwt: false`
- [ ] Webhook monitoring functions deployed
- [ ] Email alerts configured
- [ ] Admin dashboard shows healthy status

### ❌ Dangerous Configuration
- [ ] "Verify JWT with legacy secret" = **ENABLED** ← This breaks webhooks!
- [ ] Missing environment variables
- [ ] Webhook function inaccessible

## ALERT SYSTEM

### 🔴 Critical Alerts (Immediate Action)
- **JWT verification enabled** → Disable immediately
- **Webhook function inaccessible** → Check deployment

### 🟡 Warning Alerts (Investigate Soon)
- **Multiple webhook failures** → Check Stripe logs
- **No recent successful webhooks** → Monitor payment flow

## EMERGENCY CONTACTS

### If Payments Are Not Processing:
1. **Check Supabase settings** (JWT verification OFF)
2. **Check admin dashboard** (webhook health status)
3. **Check Stripe dashboard** (webhook delivery attempts)
4. **Run manual health check** (admin dashboard button)

### Quick Health Check Commands:
```bash
# Test webhook monitor
curl -X POST https://peuelelfodoiyvmjscfg.supabase.co/functions/v1/webhook-monitor

# Check recent webhook events
supabase db diff --schema public
```

## SUCCESS INDICATORS

### ✅ System is Healthy When:
- Webhook health status shows **HEALTHY**
- JWT verification is **DISABLED**
- Recent successful webhooks visible
- Zero 401 errors in webhook logs
- Payments processing correctly

### ❌ System Needs Attention When:
- Webhook health status shows **CRITICAL** or **WARNING**
- JWT verification is **ENABLED**
- Multiple webhook failures
- 401 errors in logs
- Payments not completing after Stripe checkout

## PREVENTION

### Daily Monitoring:
- Check admin dashboard webhook status
- Verify recent successful payments
- Monitor alert emails

### Weekly Tasks:
- Review webhook failure logs
- Test alert system
- Verify Supabase settings unchanged

### Monthly Tasks:
- Review monitoring system performance
- Update alert thresholds if needed
- Test emergency procedures

---

## 📞 EMERGENCY SUMMARY

**If webhooks are failing RIGHT NOW:**

1. **Disable "Verify JWT with legacy secret"** in Supabase
2. **Deploy monitoring system**: `./scripts/deploy-webhook-monitoring.sh`
3. **Check admin dashboard** for health status
4. **Test a payment** to verify fix

**This should resolve the issue within 5 minutes.**

---

*Keep this guide accessible for quick reference during emergencies.*
