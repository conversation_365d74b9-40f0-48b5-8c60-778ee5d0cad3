import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '../../lib/supabase';
import { Al<PERSON><PERSON>riangle, CheckCircle, XCircle, Refresh<PERSON>w, Clock, Zap } from 'lucide-react';

interface WebhookHealthStatus {
  health_status: 'HEALTHY' | 'WARNING' | 'CRITICAL';
  status_message: string;
  check_timestamp: string;
  jwt_verification_enabled: boolean;
  webhook_function_accessible: boolean;
  failed_webhooks_count_24h: number;
  last_successful_webhook: string | null;
}

interface WebhookHealthCheck {
  id: string;
  check_timestamp: string;
  jwt_verification_enabled: boolean;
  webhook_function_accessible: boolean;
  last_successful_webhook: string | null;
  failed_webhooks_count_24h: number;
  alert_sent: boolean;
  error_details: string | null;
}

export const WebhookHealthMonitor: React.FC = () => {
  const [isRunningCheck, setIsRunningCheck] = useState(false);
  const queryClient = useQueryClient();

  // Query for latest health status
  const { data: healthStatus, isLoading, error, refetch } = useQuery({
    queryKey: ['webhook-health-status'],
    queryFn: async (): Promise<WebhookHealthStatus> => {
      const { data, error } = await supabase
        .rpc('get_latest_webhook_health');

      if (error) throw error;
      if (!data || data.length === 0) {
        throw new Error('Keine Webhook-Gesundheitsdaten verfügbar');
      }

      return data[0];
    },
    refetchInterval: 30000, // Refetch every 30 seconds
    staleTime: 15000, // Consider data stale after 15 seconds
  });

  // Query for recent health checks
  const { data: recentChecks } = useQuery({
    queryKey: ['webhook-health-checks'],
    queryFn: async (): Promise<WebhookHealthCheck[]> => {
      const { data, error } = await supabase
        .from('webhook_health_checks')
        .select('*')
        .order('check_timestamp', { ascending: false })
        .limit(10);

      if (error) throw error;
      return data || [];
    },
    refetchInterval: 60000, // Refetch every minute
  });

  // Mutation to run manual health check
  const runHealthCheckMutation = useMutation({
    mutationFn: async () => {
      const { data, error } = await supabase.functions.invoke('webhook-monitor', {
        body: { manual: true }
      });

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['webhook-health-status'] });
      queryClient.invalidateQueries({ queryKey: ['webhook-health-checks'] });
    },
    onError: (error) => {
      console.error('Error running health check:', error);
    },
    onSettled: () => {
      setIsRunningCheck(false);
    }
  });

  const handleRunHealthCheck = () => {
    setIsRunningCheck(true);
    runHealthCheckMutation.mutate();
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'HEALTHY':
        return <CheckCircle className="w-6 h-6 text-green-500" />;
      case 'WARNING':
        return <AlertTriangle className="w-6 h-6 text-yellow-500" />;
      case 'CRITICAL':
        return <XCircle className="w-6 h-6 text-red-500" />;
      default:
        return <Clock className="w-6 h-6 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'HEALTHY':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'WARNING':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'CRITICAL':
        return 'bg-red-50 border-red-200 text-red-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('de-DE');
  };

  const getTimeSince = (timestamp: string) => {
    const now = new Date();
    const then = new Date(timestamp);
    const diffMs = now.getTime() - then.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'gerade eben';
    if (diffMins < 60) return `vor ${diffMins} Min`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `vor ${diffHours} Std`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `vor ${diffDays} Tag${diffDays > 1 ? 'en' : ''}`;
  };

  if (isLoading) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center space-x-2">
          <RefreshCw className="w-5 h-5 animate-spin text-gray-500" />
          <span className="text-gray-600">Webhook-Status wird geladen...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center space-x-2 text-red-600">
          <XCircle className="w-5 h-5" />
          <span>Fehler beim Laden des Webhook-Status: {error.message}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Current Status Card */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <Zap className="w-5 h-5 mr-2" />
            Webhook-Gesundheitsstatus
          </h3>
          <button
            onClick={handleRunHealthCheck}
            disabled={isRunningCheck}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isRunningCheck ? 'animate-spin' : ''}`} />
            {isRunningCheck ? 'Prüfung läuft...' : 'Jetzt prüfen'}
          </button>
        </div>

        {healthStatus && (
          <div className={`rounded-lg border-2 p-4 ${getStatusColor(healthStatus.health_status)}`}>
            <div className="flex items-center space-x-3">
              {getStatusIcon(healthStatus.health_status)}
              <div className="flex-1">
                <h4 className="font-medium">
                  Status: {healthStatus.health_status === 'HEALTHY' ? 'Gesund' : 
                           healthStatus.health_status === 'WARNING' ? 'Warnung' : 'Kritisch'}
                </h4>
                <p className="text-sm mt-1">{healthStatus.status_message}</p>
                <p className="text-xs mt-2 opacity-75">
                  Letzte Prüfung: {formatTimestamp(healthStatus.check_timestamp)} 
                  ({getTimeSince(healthStatus.check_timestamp)})
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Detailed Status Information */}
        {healthStatus && (
          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-sm font-medium text-gray-500">JWT-Verifizierung</div>
              <div className={`text-lg font-semibold ${healthStatus.jwt_verification_enabled ? 'text-red-600' : 'text-green-600'}`}>
                {healthStatus.jwt_verification_enabled ? '🔴 Aktiviert' : '✅ Deaktiviert'}
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-sm font-medium text-gray-500">Webhook-Funktion</div>
              <div className={`text-lg font-semibold ${healthStatus.webhook_function_accessible ? 'text-green-600' : 'text-red-600'}`}>
                {healthStatus.webhook_function_accessible ? '✅ Erreichbar' : '🔴 Nicht erreichbar'}
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-sm font-medium text-gray-500">Fehler (24h)</div>
              <div className={`text-lg font-semibold ${healthStatus.failed_webhooks_count_24h > 0 ? 'text-yellow-600' : 'text-green-600'}`}>
                {healthStatus.failed_webhooks_count_24h}
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-sm font-medium text-gray-500">Letzter Erfolg</div>
              <div className="text-sm font-semibold text-gray-900">
                {healthStatus.last_successful_webhook 
                  ? getTimeSince(healthStatus.last_successful_webhook)
                  : 'Unbekannt'
                }
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Critical Alert */}
      {healthStatus?.jwt_verification_enabled && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <XCircle className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Kritisches Problem: JWT-Verifizierung aktiviert
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>
                  Die Einstellung "Verify JWT with legacy secret" ist in Supabase aktiviert. 
                  Dies verhindert, dass Stripe-Webhooks funktionieren und Zahlungen verarbeitet werden.
                </p>
                <div className="mt-3">
                  <p className="font-medium">Sofortige Lösung:</p>
                  <ol className="list-decimal list-inside mt-1 space-y-1">
                    <li>Gehen Sie zu Ihrem Supabase Dashboard</li>
                    <li>Navigieren Sie zu Settings → API</li>
                    <li>Deaktivieren Sie "Verify JWT with legacy secret"</li>
                  </ol>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Recent Health Checks */}
      {recentChecks && recentChecks.length > 0 && (
        <div className="bg-white shadow rounded-lg p-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Letzte Gesundheitsprüfungen</h4>
          <div className="space-y-2">
            {recentChecks.slice(0, 5).map((check) => (
              <div key={check.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(
                    check.jwt_verification_enabled || !check.webhook_function_accessible ? 'CRITICAL' :
                    check.failed_webhooks_count_24h >= 3 ? 'WARNING' : 'HEALTHY'
                  )}
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {formatTimestamp(check.check_timestamp)}
                    </div>
                    {check.error_details && (
                      <div className="text-xs text-red-600">{check.error_details}</div>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm text-gray-500">
                    Fehler: {check.failed_webhooks_count_24h}
                  </div>
                  {check.alert_sent && (
                    <div className="text-xs text-blue-600">Alert gesendet</div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
